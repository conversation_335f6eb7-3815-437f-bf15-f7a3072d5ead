# Hệ thống Embedding Thông minh

## Tổng quan

Hệ thống đã được cập nhật để xử lý embedding một cách thông minh, đ<PERSON><PERSON> bảo tính nhất quán của vector trong cùng một file và có khả năng retry khi gặp lỗi.

## Logic Embedding Mới (Cập nhật: Jina First)

### 1. Phát hiện lỗi ngay từ đầu
- **Nếu Jina lỗi ngay từ đầu** (file chưa có embedding nào) → **<PERSON><PERSON><PERSON><PERSON> sang Gemini**
- Điều này đảm bảo file vẫn được xử lý thành công ngay cả khi Jina không khả dụng

### 2. Xử lý lỗi giữa chừng
- **Nếu Jina đã chạy được rồi mà bị lỗi giữa chừng** → **Retry với Jina** (kh<PERSON><PERSON> chuyển sang Gemini)
- **Nếu Gemini đã chạy được rồi mà bị lỗi giữa chừng** → **Retry với Gemini** (khô<PERSON> chuyển sang Jina)
- Lý do: Vector của Gemini và Jina khác nhau, việc trộn lẫn sẽ làm giảm chất lượng tìm kiếm

### 3. Duy trì tính nhất quán
- Hệ thống kiểm tra embedding source đã sử dụng cho file
- Nếu file đã có embedding từ một source, các chunk mới sẽ sử dụng cùng source đó

## Cách hoạt động

### Function `get_embedding_with_source`

```python
def get_embedding_with_source(text: str, preferred_source: str = None, file_id: str = None):
    """
    Logic thông minh:
    1. Nếu có preferred_source → sử dụng source đó
    2. Nếu không có preferred_source → thử Gemini trước (mặc định)
    3. Nếu Gemini lỗi:
       - Kiểm tra file đã có embedding chưa
       - Nếu chưa có → fallback sang Jina
       - Nếu đã có Gemini → retry Gemini (không fallback)
       - Nếu đã có Jina → sử dụng Jina
    """
```

### Function `get_file_embedding_source`

```python
def get_file_embedding_source(file_id: str) -> str:
    """
    Kiểm tra embedding source đã sử dụng cho file
    Returns: 'gemini', 'jina', hoặc 'none'
    """
```

## Retry Mechanism với Huey

### Task Configuration
```python
@huey.task(retries=3, retry_delay=60)  # Retry 3 lần, mỗi lần cách 60 giây
def process_file_task(...):
```

### Khi nào retry?
- **Gemini lỗi giữa chừng**: Task sẽ retry với Gemini
- **Lỗi khác**: Task sẽ retry toàn bộ quá trình
- **Sau 3 lần retry**: Task sẽ fail và được đánh dấu lỗi

## Ưu điểm

### 1. Tính nhất quán Vector
- Tất cả chunks trong cùng file sử dụng cùng embedding source
- Đảm bảo chất lượng tìm kiếm semantic tốt nhất

### 2. Khả năng phục hồi
- Nếu Gemini lỗi ngay từ đầu → chuyển sang Jina
- Nếu Gemini lỗi giữa chừng → retry để hoàn thành

### 3. Tối ưu hiệu suất
- Parallel processing với file context
- Intelligent fallback chỉ khi cần thiết

## Logging

Hệ thống sẽ log chi tiết:
- Embedding source được chọn
- Lý do fallback hoặc retry
- Kết quả của từng bước

### Ví dụ Log:
```
INFO - Creating embedding for text (length: 1500 chars)
INFO - File ID for context: file-abc123
INFO - Attempting to create embedding using Gemini (default)...
ERROR - Gemini embedding failed: 429 Resource has been exhausted
INFO - Found existing embedding source for file file-abc123: gemini
WARNING - File file-abc123 already has Gemini embeddings. Not falling back to Jina to maintain consistency.
ERROR - Gemini embedding failed for file with existing Gemini embeddings: 429 Resource has been exhausted
```

## Cấu hình

### Embedding Dimensions
- Tất cả embedding được chuẩn hóa về 2048 dimensions
- Gemini: padding từ 768 → 2048
- Jina: padding từ 1024 → 2048

### Retry Settings
- Số lần retry: 3
- Delay giữa các lần retry: 60 giây
- Exponential backoff: Có thể cấu hình thêm

## Monitoring

### Progress Tracking
- Task progress được lưu trong SQLite
- Real-time updates qua API endpoints
- Chi tiết lỗi và retry attempts

### Metrics
- Số lượng embedding thành công/thất bại
- Phân bố theo source (Gemini/Jina)
- Thời gian xử lý trung bình
